'use server'

import { action } from '@/lib/actions/action'
import { db } from '@/src/db/db'
import { z } from 'zod'

export const getCertificateStatus = action

	.input(z.object({ customerId: z.string().uuid() }))

	.output(z.object({ isValid: z.boolean() }))

	.handler(async ({ input: { customerId } }) => {
		const [secret] = await db.secrets
			.where({
				id: customerId,
			})
			.select('description')

		if (!secret?.description) return { isValid: false }

		try {
			const metadata = JSON.parse(secret.description)
			const isIcpBrasilCertificate = metadata.type === 'icp-brasil-certificate'
			const hasValidExpiration = metadata.expiration ? new Date(metadata.expiration) > new Date() : false

			return { isValid: isIcpBrasilCertificate && hasValidExpiration }
		} catch {
			return { isValid: false }
		}
	})
