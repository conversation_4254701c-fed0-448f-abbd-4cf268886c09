'use server'

import { action } from '@/lib/actions/action'
import { db } from '@/src/db/db'
import { CustomersSecretsInputSchema } from '@/src/db/tables/customersSecrets.schemas'
import { z } from 'zod'

export const getCertificateStatus = action

	.input(z.object({ customerId: CustomersSecretsInputSchema.shape.customerId }))

	.output(z.object({ isValid: z.boolean() }))

	.handler(async ({ input: { customerId } }) => {
		const [data] = await db.secrets.pluck('expiration').where({
			id: customerId,
			description: 'icp-brasil-certificate',
		})

		return { isValid: !!data }
	})
