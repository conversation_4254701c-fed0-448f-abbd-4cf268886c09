import 'server-only'

import { CustomersSecretsSelectSchema } from '@/db/schema/customersSecrets'
import { action } from '@/lib/actions/action'
import { getSupabaseAdmin } from '@/lib/supabase/admin-client'
import { z } from 'zod'

export const put = action
	.input(CustomersSecretsSelectSchema.omit({ expiration: true }).extend({ data: z.string().optional() }))
	.handler(async ({ input: { customerId, data, type } }) => {
		if (!data) return

		const supabase = getSupabaseAdmin()
		const name = `${customerId}-${type}`

		const { data: existing, error: checkError } = await supabase
			.from('vault.decrypted_secrets')
			.select('id')
			.eq('id', customerId)
			.eq('description', type)
			.maybeSingle()

		if (checkError) throw new Error(`Failed to check existing secret: ${checkError.message}`)

		const rpcCall = existing
			? supabase.rpc('vault_update_secret', { secret_id: customerId, secret: data, name, description: type })
			: supabase.rpc('vault_create_secret', { secret_id: customerId, secret: data, name, description: type })

		const { data: result, error } = await rpcCall
		if (error) throw new Error(`Failed to ${existing ? 'update' : 'create'} secret: ${error.message}`)
		return result
	})
