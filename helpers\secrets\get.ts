import 'server-only'

import { action } from '@/lib/actions/action'
import { customersSecretsTypesMapping } from '@/src/db/tables/customersSecrets.helpers'
import { DefaultAzureCredential } from '@azure/identity'
import { SecretClient } from '@azure/keyvault-secrets'
import { z } from 'zod'

const KEY_VAULT_NAME = process.env.KEY_VAULT_NAME
const URL = `https://${KEY_VAULT_NAME}.vault.azure.net`

// Schema for legacy Azure Key Vault access
const LegacySecretsInputSchema = z.object({
	customerId: z.string().uuid(),
	type: z.enum(customersSecretsTypesMapping),
})

export const get = action

	.input(LegacySecretsInputSchema)

	.handler(async ({ input: { customerId, type } }) => {
		const credential = new DefaultAzureCredential()

		if (!KEY_VAULT_NAME) throw new Error('Key vault name is empty')

		const client = new SecretClient(URL, credential)

		const result = await client.getSecret(`${customerId}-${type}`)

		return {
			...result,
			value: result.value?.replaceAll('data:application/x-pkcs12;base64,', ''), // remove the data:application/x-pkcs12;base64, prefix. It was mistakingly added, but it lead to several bugs.,
		}
	})
